use mysql::prelude::*;
use mysql::*;
use serde_derive::Deserialize;
use std::fs;
use std::io::{self, Write};

#[derive(Debug, Deserialize)]
struct Config {
    database: DatabaseConfig,
}

#[derive(Debug, Deserialize)]
struct DatabaseConfig {
    host: String,
    port: u16,
    username: String,
    password: String,
    database: String,
    table_prefix: String,
}

fn clean_database() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let config_content = fs::read_to_string("src/configs/Config_TEST.toml")?;
    let config: Config = toml::from_str(&config_content)?;

    let url = format!(
        "mysql://{}:{}@{}:{}/{}",
        config.database.username,
        config.database.password,
        config.database.host,
        config.database.port,
        config.database.database
    );

    let pool = Pool::new(url.as_str())?;
    let mut conn = pool.get_conn()?;

    let tables = vec![
        "admin_log",
        "attachment",
        "signin",
        "sms",
        "user_binding",
        "user_money_log",
        "user_score_log",
        "user_token",
        "wanlshop_card_pay",
        "wanlshop_cart",
        "wanlshop_chat",
        "wanlshop_coupon_receive",
        "wanlshop_fenxiao",
        "wanlshop_pay",
        "wanlshop_record",
        "wanlshop_order",
        "wanlshop_order_goods",
        "wanlshop_order_address",
        "wanlshop_refund",
        "wanlshop_refund_log",
        "wanlshop_find_user",
        "wanlshop_find_user_follow",
        "wanlshop_goods_comment",
        "wanlshop_goods_follow",
        "wanlshop_notice",
        "wanlshop_search",
        "wanlshop_card_redeem",
        "wanlshop_card_order_goods",
        "wanlshop_card_order_address",
    ];

    for table in &tables {
        let table_name = format!("{}{}", config.database.table_prefix, table);
        conn.query_drop(format!("TRUNCATE TABLE {}", table_name))?;
        println!("表 {} 已成功清空！", table_name);
    }

    println!("所有表清空操作已完成！");
    Ok(())
}

fn clean_database_exclude_binding() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let config_content = fs::read_to_string("src/configs/Config_TEST.toml")?;
    let config: Config = toml::from_str(&config_content)?;

    let url = format!(
        "mysql://{}:{}@{}:{}/{}",
        config.database.username,
        config.database.password,
        config.database.host,
        config.database.port,
        config.database.database
    );

    let pool = Pool::new(url.as_str())?;
    let mut conn = pool.get_conn()?;

    let tables = vec![
        "admin_log",
        "attachment",
        "signin",
        "sms",
        "user_money_log",
        "user_score_log",
        "user_token",
        "wanlshop_card_pay",
        "wanlshop_cart",
        "wanlshop_chat",
        "wanlshop_coupon_receive",
        "wanlshop_fenxiao",
        "wanlshop_pay",
        "wanlshop_record",
        "wanlshop_order",
        "wanlshop_order_goods",
        "wanlshop_order_address",
        "wanlshop_refund",
        "wanlshop_refund_log",
        "wanlshop_find_user",
        "wanlshop_find_user_follow",
        "wanlshop_goods_comment",
        "wanlshop_goods_follow",
        "wanlshop_notice",
        "wanlshop_search",
        "wanlshop_card_redeem",
        "wanlshop_card_order_goods",
        "wanlshop_card_order_address",
    ];

    for table in &tables {
        let table_name = format!("{}{}", config.database.table_prefix, table);
        conn.query_drop(format!("TRUNCATE TABLE {}", table_name))?;
        println!("表 {} 已成功清空！", table_name);
    }

    println!("所有表清空操作已完成！(不包含user_binding)");
    Ok(())
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    loop {
        println!("请选择您要执行的操作：");
        println!("1. 清理数据库");
        println!("2. 不清理绑定数据");
        println!("q. 退出程序");

        let mut choice = String::new();
        io::stdout().flush()?;
        io::stdin().read_line(&mut choice)?;

        match choice.trim() {
            "1" => {
                if let Err(e) = clean_database() {
                    println!("清理数据库时出错: {}", e);
                }
            }
            "2" => {
                if let Err(e) = clean_database_exclude_binding() {
                    println!("清理数据库时出错: {}", e);
                }
            }
            "q" => {
                println!("退出程序...");
                break;
            }
            _ => {
                println!("无效的选择，请重新输入！");
            }
        }
    }

    Ok(())
}
