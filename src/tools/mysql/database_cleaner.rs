use mysql::prelude::*;
use mysql::*;
use crate::database::mysql::config::Config;
use crate::database::mysql::connection::{get_singleton_conn};
use crate::database::mysql::tables::ALL_TABLES;

fn clean_tables(conn: &mut PooledConn, config: &Config, tables: &[&str], exclude_binding: bool) -> std::result::Result<(), Box<dyn std::error::Error>> {
    for table in tables {
        if exclude_binding && *table == "user_binding" ||  {
            continue;
        }
        let table_name = format!("{}{}", config.database.table_prefix, table);
        conn.query_drop(format!("TRUNCATE TABLE {}", table_name))?;
        println!("表 {} 已成功清空！", table_name);
    }
    Ok(())
}

fn clean_database_internal(exclude_binding: bool) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let (mut conn, config) = get_singleton_conn()?;
    clean_tables(&mut conn, &config, ALL_TABLES, exclude_binding)?;
    if exclude_binding {
        println!("所有表清空操作已完成！(不包含user_binding)");
    } else {
        println!("所有表清空操作已完成！");
    }
    Ok(())
}

pub fn clean_database() -> std::result::Result<(), Box<dyn std::error::Error>> {
    clean_database_internal(false)
}

pub fn clean_database_exclude_binding() -> std::result::Result<(), Box<dyn std::error::Error>> {
    clean_database_internal(true)
} 